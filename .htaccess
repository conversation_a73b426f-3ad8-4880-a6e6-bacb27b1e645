# Galaxy Generator - Apache Configuration

# Enable rewrite engine
RewriteEngine On

# Redirect to index.php if accessing root
DirectoryIndex index.php

# API routes - không cần rewrite vì đã có file .php
# /api/create-galaxy.php, /api/get-galaxy.php, etc. sẽ hoạt động trực tiếp

# Security headers
<IfModule mod_headers.c>
    # CORS headers for API
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type"
    
    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# File upload limits
<IfModule mod_php.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_time 300
</IfModule>

# Protect sensitive files
<Files "*.sql">
    Order allow,deny
    <PERSON>y from all
</Files>

<Files "config/*.php">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

# Allow access to uploads and songs
<Directory "uploads">
    Options -Indexes
    AllowOverride None
    Order allow,deny
    Allow from all
</Directory>

<Directory "songs">
    Options -Indexes
    AllowOverride None
    Order allow,deny
    Allow from all
</Directory>

# Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Cache static files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType audio/mpeg "access plus 1 month"
    ExpiresByType audio/mp3 "access plus 1 month"
</IfModule>

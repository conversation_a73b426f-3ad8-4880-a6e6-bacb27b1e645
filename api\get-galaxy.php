<?php
/**
 * API endpoint để lấy thông tin galaxy
 */

// Bắt tất cả errors và chuyển thành JSON
error_reporting(E_ALL);
ini_set('display_errors', 0);

// Set headers trước khi có bất kỳ output nào
header('Content-Type: application/json; charset=utf-8');

// Include config trước để có CORS_ORIGIN
try {
    require_once __DIR__ . '/../config/database.php';
    header('Access-Control-Allow-Origin: ' . CORS_ORIGIN);
} catch (Exception $e) {
    header('Access-Control-Allow-Origin: *');
}

header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Chỉ cho phép GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit();
}

try {
    // Include các file cần thiết (config đã được include ở trên)
    require_once __DIR__ . '/../classes/Galaxy.php';
    
    // Lấy galaxy ID từ URL parameter
    $galaxyId = $_GET['id'] ?? '';
    
    if (empty($galaxyId)) {
        throw new Exception('Galaxy ID is required');
    }
    
    // Validate galaxy ID format
    if (!preg_match('/^[a-zA-Z0-9]{' . GALAXY_ID_LENGTH . '}$/', $galaxyId)) {
        throw new Exception('Invalid galaxy ID format');
    }
    
    // Lấy thông tin galaxy
    $galaxy = new Galaxy();
    $data = $galaxy->getGalaxyById($galaxyId);
    
    if ($data === null) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'Galaxy not found'
        ], JSON_UNESCAPED_SLASHES);
    } else {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'data' => $data
        ], JSON_UNESCAPED_SLASHES);
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_SLASHES);
}
?>

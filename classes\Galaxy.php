<?php
/**
 * Class Galaxy - Xử lý logic galaxy
 */

require_once __DIR__ . '/Database.php';

class Galaxy {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Tạo galaxy mới
     */
    public function createGalaxy($data) {
        try {
            $this->db->beginTransaction();
            
            // Tạo ID duy nhất
            do {
                $galaxyId = Database::generateGalaxyId();
                $existing = $this->getGalaxyById($galaxyId);
            } while ($existing !== null);
            
            // Validate và sanitize dữ liệu
            $colors = $this->validateColors($data['colors'] ?? []);
            $messages = $this->validateMessages($data['messages'] ?? []);
            $icons = $this->validateIcons($data['icons'] ?? []);
            $song = Database::sanitizeString($data['song'] ?? '');
            
            // Insert galaxy chính
            $sql = "INSERT INTO galaxies (id, song, love_color, birthday_color, date_color, special_color, heart_color, performance_level)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

            $performance = $data['performance'] ?? 'medium';
            if (!in_array($performance, ['low', 'medium', 'high'])) {
                $performance = 'medium';
            }

            $this->db->insert($sql, [
                $galaxyId,
                $song,
                $colors['love'],
                $colors['birthday'],
                $colors['date'],
                $colors['special'],
                $colors['heart'],
                $performance
            ]);
            
            // Insert messages
            if (!empty($messages)) {
                $this->insertMessages($galaxyId, $messages);
            }
            
            // Insert icons
            if (!empty($icons)) {
                $this->insertIcons($galaxyId, $icons);
            }

            // Insert images
            if (!empty($data['images'])) {
                $this->insertImages($galaxyId, $data['images']);
            }

            $this->db->commit();
            
            return [
                'success' => true,
                'galaxy_id' => $galaxyId,
                'url' => $this->getGalaxyUrl($galaxyId)
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Lấy thông tin galaxy theo ID
     */
    public function getGalaxyById($galaxyId) {
        try {
            // Lấy thông tin galaxy chính
            $galaxy = $this->db->selectOne(
                "SELECT * FROM galaxies WHERE id = ?", 
                [$galaxyId]
            );
            
            if (!$galaxy) {
                return null;
            }
            
            // Lấy messages
            $messages = $this->db->select(
                "SELECT message FROM galaxy_messages WHERE galaxy_id = ? ORDER BY id",
                [$galaxyId]
            );
            
            // Lấy icons
            $icons = $this->db->select(
                "SELECT icon FROM galaxy_icons WHERE galaxy_id = ? ORDER BY id",
                [$galaxyId]
            );
            
            // Lấy images
            $images = $this->db->select(
                "SELECT image_url FROM galaxy_images WHERE galaxy_id = ? ORDER BY id",
                [$galaxyId]
            );
            
            // Format dữ liệu theo cấu trúc cũ
            return [
                'messages' => array_column($messages, 'message'),
                'icons' => array_column($icons, 'icon'),
                'colors' => [
                    'love' => $galaxy['love_color'],
                    'birthday' => $galaxy['birthday_color'],
                    'date' => $galaxy['date_color'],
                    'special' => $galaxy['special_color'],
                    'heart' => $galaxy['heart_color']
                ],
                'images' => array_column($images, 'image_url'),
                'song' => $galaxy['song'],
                'performance' => $galaxy['performance_level'] ?? 'medium',
                'createdAt' => $galaxy['created_at']
            ];
            
        } catch (Exception $e) {
            throw new Exception("Error getting galaxy: " . $e->getMessage());
        }
    }
    
    /**
     * Thêm ảnh vào galaxy
     */
    public function addImages($galaxyId, $imageUrls) {
        try {
            foreach ($imageUrls as $imageUrl) {
                $this->db->insert(
                    "INSERT INTO galaxy_images (galaxy_id, image_url) VALUES (?, ?)",
                    [$galaxyId, $imageUrl]
                );
            }
            return true;
        } catch (Exception $e) {
            throw new Exception("Error adding images: " . $e->getMessage());
        }
    }
    
    /**
     * Validate màu sắc
     */
    private function validateColors($colors) {
        $defaultColors = [
            'love' => '#ff6b9d',
            'birthday' => '#4ecdc4',
            'date' => '#c534ed',
            'special' => '#f34bce',
            'heart' => '#ff69b4'
        ];
        
        $validatedColors = [];
        foreach ($defaultColors as $key => $default) {
            $color = $colors[$key] ?? $default;
            $validatedColors[$key] = Database::validateHexColor($color) ? $color : $default;
        }
        
        return $validatedColors;
    }
    
    /**
     * Validate tin nhắn
     */
    private function validateMessages($messages) {
        $validated = [];
        $count = 0;
        
        foreach ($messages as $message) {
            if ($count >= MAX_MESSAGES) break;
            
            $message = Database::sanitizeString($message);
            if (!empty($message) && strlen($message) <= 500) {
                $validated[] = $message;
                $count++;
            }
        }
        
        return $validated;
    }
    
    /**
     * Validate biểu tượng
     */
    private function validateIcons($icons) {
        $validated = [];
        $count = 0;
        
        foreach ($icons as $icon) {
            if ($count >= MAX_ICONS) break;
            
            $icon = trim($icon);
            if (!empty($icon) && mb_strlen($icon) <= 10) {
                $validated[] = $icon;
                $count++;
            }
        }
        
        return $validated;
    }
    
    /**
     * Insert messages
     */
    private function insertMessages($galaxyId, $messages) {
        foreach ($messages as $message) {
            $this->db->insert(
                "INSERT INTO galaxy_messages (galaxy_id, message) VALUES (?, ?)",
                [$galaxyId, $message]
            );
        }
    }
    
    /**
     * Insert icons
     */
    private function insertIcons($galaxyId, $icons) {
        foreach ($icons as $icon) {
            $this->db->insert(
                "INSERT INTO galaxy_icons (galaxy_id, icon) VALUES (?, ?)",
                [$galaxyId, $icon]
            );
        }
    }

    /**
     * Insert images
     */
    private function insertImages($galaxyId, $images) {
        foreach ($images as $imageUrl) {
            $this->db->insert(
                "INSERT INTO galaxy_images (galaxy_id, image_url) VALUES (?, ?)",
                [$galaxyId, $imageUrl]
            );
        }
    }
    
    /**
     * Tạo URL galaxy
     */
    private function getGalaxyUrl($galaxyId) {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

        // Lấy base path một cách an toàn hơn
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';

        // Loại bỏ query string nếu có
        $requestPath = parse_url($requestUri, PHP_URL_PATH);

        // Tìm vị trí của /api/ và lấy phần trước đó
        $apiPos = strpos($requestPath, '/api/');
        if ($apiPos !== false) {
            $basePath = substr($requestPath, 0, $apiPos);
        } else {
            // Fallback: lấy thư mục cha của thư mục hiện tại
            $basePath = dirname($_SERVER['SCRIPT_NAME'] ?? '');
            $basePath = dirname($basePath); // Lùi 1 cấp từ /api
        }

        // Chuẩn hóa base path
        $basePath = '/' . trim($basePath, '/');

        // Nếu basePath chỉ là '/' thì để trống
        if ($basePath === '/') {
            $basePath = '';
        }

        // Tạo URL cuối cùng
        $url = $protocol . '://' . $host . $basePath . '/galaxy-viewer.php?id=' . $galaxyId;

        // Chuẩn hóa URL - loại bỏ các slash thừa và ký tự lạ
        $url = preg_replace('#(?<!:)//+#', '/', $url); // Loại bỏ double slashes
        $url = str_replace('/\/', '/', $url); // Loại bỏ /\/
        $url = str_replace('\\/', '/', $url); // Loại bỏ \/
        $url = preg_replace('#/+#', '/', $url); // Loại bỏ multiple slashes

        // Đảm bảo protocol vẫn có ://
        $url = str_replace(':/', '://', $url);

        return $url;
    }
}
?>

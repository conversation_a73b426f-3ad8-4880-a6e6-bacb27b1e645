// Lấy galaxy ID từ URL
const urlParams = new URLSearchParams(window.location.search);
const galaxyId = urlParams.get("id");
const isDemo = urlParams.get("demo") === "1";

const demoGalaxyData = {
  messages: [
    "I love you so much! ❤️",
    "Our Anniverasry",
    "I love you 💖",
    "25/08/2004",
    "Thank you for being my sunshine ",
    "Thank you for being my everything 💕",
    "You are my universe ",
    "There is no other",
    "You're amazing",
    "You make my heart smile ",
    "Love ya! 💖",
    "Honey bunch, you are my everything! ",
  ],
  icons: ["♥", "💖", "❤️", "❤️", "💕", "💕"],
  colors: {
    love: "#ff6b9d",
    birthday: "#4ecdc4",
    date: "#ff69b4",
    special: "#ff6b9d",
    heart: "#ff69b4",
  },
  images: [
    "https://firebasestorage.googleapis.com/v0/b/staynowapp1.appspot.com/o/galaxy-images%2Fz6654939498839_d275d5b163f80fd572ba1403f32746fa.jpg?alt=media&token=759411f6-2c26-43ac-b1c0-bf9e31752744",
  ],
  song: "eyenoselip.mp3",
  performance: "medium",
  createdAt: "2025-05-30T00:00:00.000Z",
};

const loadingScreen = document.getElementById("loadingScreen");
const errorScreen = document.getElementById("errorScreen");
const galaxy = document.getElementById("galaxy");

let galaxyData = null;
let rotationX = 0;
let rotationY = 0;
let scale = 1;
let isDragging = false;
let lastMouseX = 0;
let lastMouseY = 0;
const activeParticles = new Set();

// Responsive settings
const isMobile = window.innerWidth <= 768;
const isSmallMobile = window.innerWidth <= 480;

// Performance settings sẽ được cập nhật sau khi load galaxy data
let maxParticles = isSmallMobile ? 150 : isMobile ? 200 : 300;
let particleInterval = isMobile ? 100 : 120;
let starCount = isSmallMobile ? 250 : isMobile ? 350 : 500;
let enableStars = true;
let enableAdvancedEffects = true;

let particleSpeedMultiplier = 1; // Tốc độ bình thường
let isHolding = false;

// Làm chậm khi ấn giữ chuột hoặc chạm
const startSlowMode = () => {
  if (!isHolding) {
    isHolding = true;
    particleSpeedMultiplier = isMobile ? 2.5 : 2.0; // Chậm lại khi giữ
    document.body.style.cursor = "grabbing";
    showHoldNotification(true);
  }
};

const endSlowMode = () => {
  if (isHolding) {
    isHolding = false;
    particleSpeedMultiplier = 1; // Về tốc độ bình thường
    document.body.style.cursor = "default";
    showHoldNotification(false);
  }
};

// Thông báo khi ấn giữ
const showHoldNotification = isHolding => {
  // Remove existing notification
  const existing = document.querySelector(".hold-notification");
  if (existing) existing.remove();

  if (isHolding) {
    const notification = document.createElement("div");
    notification.className = "hold-notification";
    notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: rgba(78, 205, 196, 0.9);
                    color: white;
                    padding: 0.5rem 1.5rem;
                    border-radius: 20px;
                    font-family: 'Orbitron', sans-serif;
                    font-size: ${isMobile ? "12px" : "14px"};
                    z-index: 10000;
                    pointer-events: none;
                    backdrop-filter: blur(10px);
                    animation: slideDown 0.3s ease-out;
                `;
    notification.textContent = "🐌 Đang giữ - Thả để tăng tốc";

    document.body.appendChild(notification);
  }
};

// Add CSS animation
const style = document.createElement("style");
style.textContent = `
            @keyframes slideDown {
                0% { opacity: 0; transform: translateX(-50%) translateY(-20px); }
                100% { opacity: 1; transform: translateX(-50%) translateY(0); }
            }
        `;
document.head.appendChild(style);

// Mouse events
document.addEventListener("mousedown", startSlowMode);
document.addEventListener("mouseup", endSlowMode);
document.addEventListener("mouseleave", endSlowMode); // Thả khi chuột ra khỏi window

// Touch events
document.addEventListener("touchstart", e => {
  if (e.touches.length === 1) {
    // Chỉ với 1 ngón tay
    startSlowMode();
  }
});

document.addEventListener("touchend", endSlowMode);
document.addEventListener("touchcancel", endSlowMode);

// Prevent scrolling and zooming
document.addEventListener(
  "touchmove",
  function (e) {
    e.preventDefault();
  },
  { passive: false }
);

document.addEventListener(
  "wheel",
  function (e) {
    e.preventDefault();
  },
  { passive: false }
);

document.addEventListener("gesturestart", function (e) {
  e.preventDefault();
});

document.addEventListener("gesturechange", function (e) {
  e.preventDefault();
});

document.addEventListener("gestureend", function (e) {
  e.preventDefault();
});

// Load galaxy data from API
async function loadGalaxyData() {
  if (isDemo) {
    galaxyData = demoGalaxyData;
    initializeGalaxy();
    return;
  }

  if (!galaxyId) {
    showError();
    return;
  }

  try {
    const api = new GalaxyAPI();
    galaxyData = await api.getGalaxy(galaxyId);
    initializeGalaxy();
  } catch (error) {
    console.error("Error loading galaxy:", error);
    showError();
  }
}

function showError() {
  loadingScreen.style.display = "none";
  errorScreen.style.display = "flex";
}

function applyPerformanceSettings(performanceLevel) {
  const settings = {
    low: {
      maxParticles: isSmallMobile ? 40 : isMobile ? 60 : 150,
      particleInterval: isMobile ? 300 : 250,
      starCount: 0, // Không có stars
      enableStars: false,
      enableAdvancedEffects: false,
      imageParticleChance: 0.1, // Giảm mạnh tỷ lệ ảnh
      enableBlur: false,
      enableShadows: false,
    },
    medium: {
      maxParticles: isSmallMobile ? 60 : isMobile ? 80 : 200,
      particleInterval: isMobile ? 250 : 180,
      starCount: isSmallMobile ? 0 : isMobile ? 50 : 200, // Tắt stars trên small mobile
      enableStars: !isSmallMobile,
      enableAdvancedEffects: !isMobile,
      imageParticleChance: isMobile ? 0.15 : 0.25,
      enableBlur: !isMobile,
      enableShadows: !isMobile,
    },
    high: {
      maxParticles: isSmallMobile ? 80 : isMobile ? 120 : 400,
      particleInterval: isMobile ? 200 : 100,
      starCount: isSmallMobile ? 50 : isMobile ? 150 : 800,
      enableStars: true,
      enableAdvancedEffects: !isSmallMobile,
      imageParticleChance: isMobile ? 0.2 : 0.35,
      enableBlur: !isMobile,
      enableShadows: !isMobile,
    },
  };

  const config = settings[performanceLevel] || settings.medium;

  maxParticles = config.maxParticles;
  particleInterval = config.particleInterval;
  starCount = config.starCount;
  enableStars = config.enableStars;
  enableAdvancedEffects = config.enableAdvancedEffects;
  window.imageParticleChance = config.imageParticleChance;
  window.enableBlur = config.enableBlur;
  window.enableShadows = config.enableShadows;

  console.log(
    `Applied ${performanceLevel} performance settings for ${
      isMobile ? "mobile" : "desktop"
    }:`,
    config
  );
}

function initializeGalaxy() {
  loadingScreen.style.display = "none";

  // Apply performance settings
  const performanceLevel = galaxyData.performance || "medium";
  applyPerformanceSettings(performanceLevel);

  // Apply custom colors to CSS
  const style = document.createElement("style");
  style.textContent = `
                .text-particle.love { color: ${galaxyData.colors.love}; text-shadow: 0 0 15px ${galaxyData.colors.love}, 0 0 25px ${galaxyData.colors.love}, 0 0 35px ${galaxyData.colors.love}, 2px 2px 6px rgba(0,0,0,0.9); }
                .text-particle.birthday { color: ${galaxyData.colors.birthday}; text-shadow: 0 0 15px ${galaxyData.colors.birthday}, 0 0 25px ${galaxyData.colors.birthday}, 0 0 35px ${galaxyData.colors.birthday}, 2px 2px 6px rgba(0,0,0,0.9); }
                .text-particle.date { color: ${galaxyData.colors.date}; text-shadow: 0 0 20px ${galaxyData.colors.date}, 0 0 30px ${galaxyData.colors.date}, 0 0 40px ${galaxyData.colors.date}, 2px 2px 6px rgba(0,0,0,0.9); }
                .text-particle.special { color: ${galaxyData.colors.special}; text-shadow: 0 0 15px ${galaxyData.colors.special}, 0 0 25px ${galaxyData.colors.special}, 0 0 35px ${galaxyData.colors.special}, 2px 2px 6px rgba(0,0,0,0.9); }
                .text-particle.heart { color: ${galaxyData.colors.heart}; text-shadow: 0 0 20px ${galaxyData.colors.heart}, 0 0 30px ${galaxyData.colors.heart}, 0 0 40px ${galaxyData.colors.heart}, 3px 3px 8px rgba(0,0,0,0.9); }
            `;
  document.head.appendChild(style);

  // Phát nhạc nếu có
  if (galaxyData.song) {
    const audio = document.getElementById("galaxyAudio");

    // Set source
    if (galaxyData.song.startsWith("http")) {
      audio.src = galaxyData.song;
    } else {
      audio.src = `songs/${galaxyData.song}`;
    }

    // Cấu hình audio để lặp lại
    audio.loop = true;
    audio.volume = 0.7; // Giảm âm lượng một chút

    // Hàm phát nhạc
    const playAudio = () => {
      audio.play().catch(error => {
        console.log("Không thể tự động phát nhạc:", error);
      });
    };

    // Thử phát ngay lập tức (có thể bị chặn)
    playAudio();

    // Phát khi user tương tác lần đầu
    const playAudioOnUserGesture = event => {
      playAudio();
      // Chỉ remove listener sau khi đã phát thành công
      audio.addEventListener(
        "play",
        () => {
          document.removeEventListener("touchstart", playAudioOnUserGesture);
          document.removeEventListener("click", playAudioOnUserGesture);
          document.removeEventListener("keydown", playAudioOnUserGesture);
          document.removeEventListener("mousemove", playAudioOnUserGesture);
        },
        { once: true }
      );
    };

    // Lắng nghe nhiều loại sự kiện để đảm bảo phát được
    document.addEventListener("touchstart", playAudioOnUserGesture);
    document.addEventListener("click", playAudioOnUserGesture);
    document.addEventListener("keydown", playAudioOnUserGesture);
    document.addEventListener("mousemove", playAudioOnUserGesture);

    // Thử phát lại nếu bị dừng bất ngờ
    audio.addEventListener("ended", () => {
      if (audio.loop) {
        setTimeout(() => {
          playAudio();
        }, 100);
      }
    });

    // Xử lý lỗi
    audio.addEventListener("error", e => {
      console.error("Lỗi phát nhạc:", e);
    });

    // Hiển thị nút điều khiển âm thanh
    const audioControl = document.getElementById("audioControl");
    audioControl.style.display = "flex";

    // Xử lý click nút điều khiển
    audioControl.addEventListener("click", () => {
      if (audio.paused) {
        playAudio();
        audioControl.textContent = "🔊";
        audioControl.classList.remove("muted");
        audioControl.title = "Tắt nhạc";
      } else {
        audio.pause();
        audioControl.textContent = "🔇";
        audioControl.classList.add("muted");
        audioControl.title = "Bật nhạc";
      }
    });

    // Cập nhật icon khi audio thay đổi trạng thái
    audio.addEventListener("play", () => {
      audioControl.textContent = "🔊";
      audioControl.classList.remove("muted");
      audioControl.title = "Tắt nhạc";
    });

    audio.addEventListener("pause", () => {
      audioControl.textContent = "🔇";
      audioControl.classList.add("muted");
      audioControl.title = "Bật nhạc";
    });
  }

  createStars();
  startParticleAnimation();

  // Hiển thị hướng dẫn đơn giản
  setTimeout(() => {
    const hint = document.createElement("div");
    hint.style.cssText = `
                    position: fixed;
                    bottom: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: rgba(0, 0, 0, 0.7);
                    color: white;
                    padding: 0.5rem 1rem;
                    border-radius: 15px;
                    font-family: 'Orbitron', sans-serif;
                    font-size: ${isMobile ? "11px" : "13px"};
                    z-index: 10000;
                    pointer-events: none;
                    backdrop-filter: blur(10px);
                    animation: fadeInOut 4s ease-in-out;
                `;
    hint.textContent = "💡 Ấn giữ để làm chậm văn bản";

    document.body.appendChild(hint);

    setTimeout(() => {
      if (hint.parentNode) {
        hint.remove();
      }
    }, 4000);
  }, 3000);
}

function getRandomMessage() {
  return galaxyData.messages[
    Math.floor(Math.random() * galaxyData.messages.length)
  ];
}

function getRandomIcon() {
  return galaxyData.icons[Math.floor(Math.random() * galaxyData.icons.length)];
}

function getMessageClass(message) {
  const lowerMessage = message.toLowerCase();
  if (
    lowerMessage.includes("love") ||
    lowerMessage.includes("yêu") ||
    lowerMessage.includes("tình")
  ) {
    return "love";
  } else if (
    lowerMessage.includes("birthday") ||
    lowerMessage.includes("sinh nhật") ||
    lowerMessage.includes("chúc mừng")
  ) {
    return "birthday";
  } else if (/\d/.test(message) || message.includes("/")) {
    return "date";
  } else {
    return "special";
  }
}

function createTextParticle() {
  if (activeParticles.size >= maxParticles) {
    return;
  }

  const isIcon = Math.random() > 0.7;
  const element = document.createElement("div");

  if (isIcon) {
    element.className = "text-particle heart";
    element.textContent = getRandomIcon();
  } else {
    const message = getRandomMessage();
    element.className = `text-particle ${getMessageClass(message)}`;
    element.textContent = message;
  }

  const xPos = Math.random() * 100;
  const zPos = (Math.random() - 0.5) * (isMobile ? 300 : 500);
  // Animation duration bình thường
  const animationDuration = Math.random() * 2 + (isMobile ? 3 : 3);

  element.style.left = xPos + "%";

  // Responsive font sizes - tối ưu cho mobile
  let baseFontSize, fontSizeVariation;
  if (isSmallMobile) {
    baseFontSize = isIcon ? 14 : 11;
    fontSizeVariation = isIcon ? 4 : 3;
  } else if (isMobile) {
    baseFontSize = isIcon ? 16 : 13;
    fontSizeVariation = isIcon ? 5 : 4;
  } else {
    baseFontSize = isIcon ? 22 : 18;
    fontSizeVariation = isIcon ? 10 : 8;
  }

  element.style.fontSize =
    Math.random() * fontSizeVariation + baseFontSize + "px";

  // Tắt một số hiệu ứng trên mobile để tăng performance
  if (isMobile && !window.enableShadows) {
    element.style.textShadow = isIcon
      ? "0 0 5px currentColor, 1px 1px 2px rgba(0,0,0,0.8)"
      : "0 0 3px currentColor, 1px 1px 2px rgba(0,0,0,0.8)";
  }

  const depthOpacity = Math.max(
    0.4,
    1 - Math.abs(zPos) / (isMobile ? 250 : 400)
  );
  element.style.opacity = depthOpacity;

  let startTime = null;
  let startY = -150;
  let endY = window.innerHeight + 150;
  const thisParticleSpeed = particleSpeedMultiplier; // Lưu multiplier tại thời điểm tạo

  function animateParticle(timestamp) {
    if (!startTime) startTime = timestamp;
    const progress =
      (timestamp - startTime) / (animationDuration * 1000 * thisParticleSpeed);

    if (progress < 1) {
      const currentY = startY + (endY - startY) * progress;
      const opacity =
        progress < 0.05
          ? progress * 20
          : progress > 0.95
          ? (1 - progress) * 20
          : depthOpacity;

      element.style.transform = `translate3d(0, ${currentY}px, ${zPos}px)`;
      element.style.opacity = opacity;

      requestAnimationFrame(animateParticle);
    } else {
      if (element.parentNode) {
        element.parentNode.removeChild(element);
        activeParticles.delete(element);
      }
    }
  }

  galaxy.appendChild(element);
  activeParticles.add(element);
  requestAnimationFrame(animateParticle);
}

function createImageParticle() {
  if (!galaxyData.images || galaxyData.images.length === 0) return;
  if (activeParticles.size >= maxParticles) return;

  const imgUrl =
    galaxyData.images[Math.floor(Math.random() * galaxyData.images.length)];
  const element = document.createElement("img");
  element.src = imgUrl;
  element.className = "image-particle";
  element.style.position = "absolute";
  // Responsive image sizes
  const imageSize = isSmallMobile ? 35 : isMobile ? 45 : 80;
  element.style.width = imageSize + "px";
  element.style.height = imageSize + "px";
  element.style.objectFit = "cover";
  element.style.borderRadius = isMobile ? "8px" : "12px";

  // Giảm box-shadow trên mobile
  if (isMobile && !window.enableShadows) {
    element.style.boxShadow = "0 0 8px #fff4, 0 1px 4px #0008";
  } else {
    element.style.boxShadow = "0 0 20px #fff8, 0 2px 8px #000a";
  }

  element.style.pointerEvents = "none";

  const xPos = Math.random() * 100;
  const zPos = (Math.random() - 0.5) * (isMobile ? 300 : 500);
  // Animation duration cho ảnh
  const animationDuration = Math.random() * 2 + (isMobile ? 3 : 3);

  element.style.left = xPos + "%";

  let startTime = null;
  let startY = -100;
  let endY = window.innerHeight + 100;
  const thisParticleSpeed = particleSpeedMultiplier; // Lưu multiplier tại thời điểm tạo

  function animateParticle(timestamp) {
    if (!startTime) startTime = timestamp;
    const progress =
      (timestamp - startTime) / (animationDuration * 1000 * thisParticleSpeed);

    if (progress < 1) {
      const currentY = startY + (endY - startY) * progress;
      const opacity =
        progress < 0.05
          ? progress * 20
          : progress > 0.95
          ? (1 - progress) * 20
          : 1;
      element.style.transform = `translate3d(0, ${currentY}px, ${zPos}px)`;
      element.style.opacity = opacity;
      requestAnimationFrame(animateParticle);
    } else {
      if (element.parentNode) {
        element.parentNode.removeChild(element);
        activeParticles.delete(element);
      }
    }
  }

  galaxy.appendChild(element);
  activeParticles.add(element);
  requestAnimationFrame(animateParticle);
}

function createStars() {
  if (!enableStars || starCount === 0) return;

  for (let i = 0; i < starCount; i++) {
    const star = document.createElement("div");
    star.className = "star";

    const angle = Math.random() * Math.PI * 10;
    const radius =
      Math.random() * (isMobile ? 800 : 1500) + (isMobile ? 200 : 300);
    const spiralHeight = Math.sin(angle) * (isMobile ? 100 : 150);

    const x = Math.cos(angle) * radius;
    const y = spiralHeight + (Math.random() - 0.5) * (isMobile ? 1000 : 2000);
    const z = Math.sin(angle) * radius * 0.5;

    star.style.left = `calc(50% + ${x}px)`;
    star.style.top = `calc(50% + ${y}px)`;
    star.style.transform = `translateZ(${z}px)`;
    star.style.animationDelay = Math.random() * 3 + "s";

    const depthBrightness = Math.max(
      0.1,
      1 - Math.abs(z) / (isMobile ? 800 : 1200)
    );
    star.style.opacity = depthBrightness;

    galaxy.appendChild(star);
  }
}

// Throttle updates trên mobile để tăng performance
let lastUpdateTime = 0;
const updateThrottle = isMobile ? 16 : 8; // 60fps trên mobile, 120fps trên desktop

function updateGalaxyTransform() {
  const now = Date.now();
  if (now - lastUpdateTime < updateThrottle) return;

  lastUpdateTime = now;
  requestAnimationFrame(() => {
    galaxy.style.transform = `translate(-50%, -50%) rotateX(${rotationX}deg) rotateY(${rotationY}deg) scale(${scale})`;
  });
}

function startParticleAnimation() {
  setInterval(() => {
    // Sử dụng imageParticleChance từ performance settings
    const chance = window.imageParticleChance || 0.25;
    if (
      galaxyData.images &&
      galaxyData.images.length > 0 &&
      Math.random() < chance
    ) {
      createImageParticle();
    } else {
      createTextParticle();
    }
  }, particleInterval);

  const initialParticles = isSmallMobile ? 8 : isMobile ? 10 : 12; // Giảm mạnh particles ban đầu trên mobile
  for (let i = 0; i < initialParticles; i++) {
    setTimeout(() => {
      const chance = window.imageParticleChance || 0.25;
      if (
        galaxyData.images &&
        galaxyData.images.length > 0 &&
        Math.random() < chance
      ) {
        createImageParticle();
      } else {
        createTextParticle();
      }
    }, i * (particleInterval * 0.6));
  }
}

// Mouse events for desktop
document.addEventListener("mousedown", e => {
  isDragging = true;
  lastMouseX = e.clientX;
  lastMouseY = e.clientY;
});

document.addEventListener("mousemove", e => {
  if (isDragging) {
    const deltaX = e.clientX - lastMouseX;
    const deltaY = e.clientY - lastMouseY;

    const sensitivity = isMobile ? 0.3 : 0.5;
    rotationY += deltaX * sensitivity;
    rotationX -= deltaY * sensitivity;

    updateGalaxyTransform();

    lastMouseX = e.clientX;
    lastMouseY = e.clientY;
  }
});

document.addEventListener("mouseup", () => {
  isDragging = false;
});

// Enhanced touch events for mobile
let initialDistance = 0;
let initialScale = 1;

document.addEventListener(
  "touchstart",
  e => {
    e.preventDefault();

    if (e.touches.length === 1) {
      isDragging = true;
      lastMouseX = e.touches[0].clientX;
      lastMouseY = e.touches[0].clientY;
    } else if (e.touches.length === 2) {
      isDragging = false;
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      initialDistance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
          Math.pow(touch2.clientY - touch1.clientY, 2)
      );
      initialScale = scale;
    }
  },
  { passive: false }
);

document.addEventListener(
  "touchmove",
  e => {
    e.preventDefault();

    if (e.touches.length === 1 && isDragging) {
      const deltaX = e.touches[0].clientX - lastMouseX;
      const deltaY = e.touches[0].clientY - lastMouseY;

      const sensitivity = 0.3;
      rotationY += deltaX * sensitivity;
      rotationX -= deltaY * sensitivity;

      updateGalaxyTransform();

      lastMouseX = e.touches[0].clientX;
      lastMouseY = e.touches[0].clientY;
    } else if (e.touches.length === 2) {
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const currentDistance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
          Math.pow(touch2.clientY - touch1.clientY, 2)
      );

      const scaleChange = currentDistance / initialDistance;
      scale = Math.max(0.5, Math.min(2, initialScale * scaleChange));

      updateGalaxyTransform();
    }
  },
  { passive: false }
);

document.addEventListener(
  "touchend",
  e => {
    e.preventDefault();
    isDragging = false;
  },
  { passive: false }
);

// Handle orientation change
window.addEventListener("orientationchange", () => {
  setTimeout(() => {
    location.reload();
  }, 100);
});

// Initialize
loadGalaxyData();

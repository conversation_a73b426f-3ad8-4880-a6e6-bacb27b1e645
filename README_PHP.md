# Galaxy Generator - PHP/MySQL Version

Đ<PERSON><PERSON> là phiên bản PHP/MySQL của Galaxy Generator, đã đư<PERSON>c chuyển đổi từ Firebase sang cơ sở dữ liệu MySQL với backend PHP.

## Yêu cầu hệ thống

- PHP 7.4 hoặc cao hơn
- MySQL 5.7 hoặc cao hơn (hoặc MariaDB)
- Web server (Apache/Nginx)
- Extension PHP: PDO, PDO_MySQL, GD (cho xử lý ảnh)

## Cài đặt

### 1. Tạo cơ sở dữ liệu

```sql
-- Chạy file database.sql để tạo cơ sở dữ liệu và bảng
mysql -u root -p < database.sql
```

### 2. Cấu hình database

Chỉnh sửa file `config/database.php`:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'galaxy_generator');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 3. <PERSON><PERSON> quyền thư mục

```bash
# T<PERSON><PERSON> thư mục uploads và phân quyền
mkdir uploads
chmod 755 uploads
chown www-data:www-data uploads  # Trên Ubuntu/Debian
```

### 4. Cấu hình web server

#### Apache (.htaccess)
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/$1.php [L]
```

#### Nginx
```nginx
location /api/ {
    try_files $uri $uri.php =404;
    fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
    fastcgi_index index.php;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    include fastcgi_params;
}
```

## Cấu trúc file

```
/
├── config/
│   └── database.php          # Cấu hình database
├── classes/
│   ├── Database.php          # Class quản lý database
│   └── Galaxy.php            # Class xử lý logic galaxy
├── api/
│   ├── create-galaxy.php     # API tạo galaxy
│   ├── get-galaxy.php        # API lấy galaxy
│   ├── upload-image.php      # API upload ảnh
│   └── upload-song.php       # API upload bài hát
├── assets/
│   └── js/
│       ├── galaxy-api.js     # JavaScript API client
│       └── galaxy-viewer.js  # Galaxy viewer logic
├── uploads/                  # Thư mục lưu ảnh upload
├── songs/                    # Thư mục lưu file nhạc
├── index.php                 # Trang chủ
├── creator.php               # Trang tạo galaxy
├── galaxy-viewer.php         # Trang xem galaxy
└── database.sql              # Script tạo database
```

## API Endpoints

### POST /api/create-galaxy.php
Tạo galaxy mới

**Request Body:**
```json
{
  "messages": ["Hello", "World"],
  "icons": ["♥", "💖"],
  "colors": {
    "love": "#ff6b9d",
    "birthday": "#4ecdc4",
    "date": "#c534ed",
    "special": "#f34bce",
    "heart": "#ff69b4"
  },
  "song": "song.mp3",
  "images": ["url1", "url2"]
}
```

**Response:**
```json
{
  "success": true,
  "galaxy_id": "abc123",
  "url": "http://domain.com/galaxy-viewer.php?id=abc123"
}
```

### GET /api/get-galaxy.php?id={galaxy_id}
Lấy thông tin galaxy

**Response:**
```json
{
  "success": true,
  "data": {
    "messages": ["Hello", "World"],
    "icons": ["♥", "💖"],
    "colors": {...},
    "images": [...],
    "song": "song.mp3",
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### POST /api/upload-image.php
Upload ảnh

**Request:** multipart/form-data với field `images[]`

**Response:**
```json
{
  "success": true,
  "urls": ["http://domain.com/uploads/image1.jpg"]
}
```

### POST /api/upload-song.php
Upload bài hát

**Request:** multipart/form-data với field `song`

**Response:**
```json
{
  "success": true,
  "filename": "unique_filename.mp3",
  "original_name": "my_song.mp3"
}
```

## Bảo mật

1. **Validation**: Tất cả input đều được validate và sanitize
2. **File Upload**: Kiểm tra loại file, kích thước, và MIME type
3. **SQL Injection**: Sử dụng prepared statements
4. **XSS**: Escape output với htmlspecialchars
5. **CORS**: Cấu hình CORS phù hợp

## Tối ưu hóa

1. **Database Indexing**: Đã tạo index cho các trường thường query
2. **File Size**: Giới hạn kích thước file upload
3. **Caching**: Có thể thêm Redis/Memcached cho caching
4. **CDN**: Upload ảnh lên CDN thay vì local storage

## Troubleshooting

### Lỗi kết nối database
- Kiểm tra thông tin kết nối trong `config/database.php`
- Đảm bảo MySQL service đang chạy
- Kiểm tra firewall và port 3306

### Lỗi upload ảnh
- Kiểm tra quyền thư mục `uploads/`
- Kiểm tra `upload_max_filesize` và `post_max_size` trong php.ini
- Đảm bảo extension GD được enable

### Lỗi 404 API
- Kiểm tra cấu hình web server
- Đảm bảo mod_rewrite được enable (Apache)
- Kiểm tra file .htaccess

## Demo

Truy cập `index.php` để xem demo và tạo galaxy mới.

## Liên hệ

Nếu có vấn đề gì, hãy liên hệ qua Facebook: https://www.facebook.com/iamtritoan/
